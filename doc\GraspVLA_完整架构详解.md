# GraspVLA完整架构详解

## 目录

1. [算法概述](#1-算法概述)
2. [数据处理流程](#2-数据处理流程)
3. [模型架构详解](#3-模型架构详解)
4. [推理流程](#4-推理流程)
5. [训练流程](#5-训练流程)
6. [数据采集](#6-数据采集)
7. [性能优化和部署](#7-性能优化和部署)

---

## 1. 算法概述

### 1.1 GraspVLA架构总览

GraspVLA是一个视觉-语言-动作(Vision-Language-Action)模型，它结合了视觉理解、语言处理和机器人动作生成能力。该算法采用多阶段方法，首先处理视觉输入，然后通过语言建模生成中间表示，最后通过流匹配生成精确的机器人动作。

```mermaid
graph TD
    A["多模态输入<br/>📷 Images: [N_views, H, W, 3]<br/>💬 Text: String<br/>🤖 Proprio: [T_proprio, D_proprio]"] --> B["数据预处理<br/>🔄 统一格式化"]
    
    B --> C["视觉特征提取<br/>🖼️ DINOv2 + SigLIP<br/>[B, N_views, 3, 224, 224]<br/>↓<br/>[B, 257, 2048]"]
    B --> D["语言处理<br/>📝 InternLM2 Tokenizer<br/>String → [seq_len]<br/>↓<br/>[B, seq_len, 4096]"]
    B --> E["本体感知处理<br/>🔧 Normalization<br/>[T_proprio, D_proprio]<br/>↓<br/>[B, T_proprio, D_proprio]"]
    
    C --> F["特征投影器<br/>🎯 MLP Projector<br/>[B, 257, 2048]<br/>↓<br/>[B, 257, 4096]"]
    
    D --> G["多模态融合<br/>🔗 Feature Concatenation<br/>[B, seq_len+257, 4096]"]
    E --> G
    F --> G
    
    G --> H["自回归生成<br/>⚡ LLM Forward Pass<br/>[B, total_seq_len, 4096]"]
    H --> I["边界框预测<br/>📦 BBox Tokens<br/>[B, N_views, 4]"]
    H --> J["目标位置预测<br/>🎯 Goal Tokens<br/>[B, 6]"]
    
    J --> K["流匹配模块<br/>🌊 Flow Matching<br/>Noise: [B, T_action, D_action]<br/>↓<br/>Action: [B, T_action, 7]"]
    E --> K
    K --> L["动作序列输出<br/>🚀 Robot Actions<br/>[x, y, z, roll, pitch, yaw, gripper]"]
    
    style A fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    style L fill:#c8e6c9,stroke:#1b5e20,stroke-width:2px
    style G fill:#fff3e0,stroke:#e65100,stroke-width:2px
    style K fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    style C fill:#fce4ec,stroke:#880e4f
    style D fill:#fff3e0,stroke:#e65100
    style F fill:#e8f5e8,stroke:#2e7d32
```

### 1.2 核心组件

- **Backbone2D (DinoSigLIPViTBackbone)**: 处理视觉输入，融合DINOv2和SigLIP特征
- **LLMBackbone (InternLM2)**: 处理语言输入，生成文本表示
- **FusedMLPProjector**: 将视觉特征投影到LLM的维度空间
- **VLAFlowMatchingModule**: 使用流匹配机制生成动作序列

### 1.3 关键技术特点

1. **多模态融合**: 通过特征投影和嵌入，有效整合视觉、语言和机器人状态信息
2. **双编码器视觉处理**: 结合DINOv2和SigLIP两个强大的视觉模型，提供更丰富的视觉表示
3. **思维链推理**: 通过自回归生成中间表示(CoT)，增强模型的推理能力
4. **流匹配动作生成**: 使用流匹配技术生成平滑、精确的动作轨迹
5. **模块化设计**: 各个组件职责清晰，便于维护和扩展

---

## 2. 数据处理流程

### 2.1 输入数据格式

根据`RawVLAData`类定义，输入数据包含以下字段：

- **instruction**: Optional[str] - 任务指令
- **images**: Optional[Dict[str, NdArray]] - 图像数据，键为图像类型，值为numpy数组
- **bboxs**: Optional[Dict[str, NdArray]] - 边界框数据，与图像对应
- **proprio**: NdArray - 本体感知数据（机器人状态）
- **action**: Optional[NdArray] - 动作数据
- **goal**: Optional[NdArray] - 目标数据

### 2.2 数据预处理详细流程

#### 2.2.1 图像预处理 (`transform_img_bbox`方法)

1. **输入**: 
   - raw_images: Dict[str, np.ndarray]，形状为[img_key数量, img_steps, H, W, C]
   - raw_bboxs: Optional[Dict[str, np.ndarray]]，形状为[img_key数量, img_steps, 4]

2. **处理步骤**:
   - 对每个图像进行resize和padding操作，目标尺寸为(config.image_size, config.image_size)
   - 使用`resize_with_bbox`函数调整图像大小并计算对应的边界框坐标
   - 应用`image_transform`进行图像变换（如归一化等）
   - 对边界框坐标进行归一化处理：bbox = bbox / image_size * 2 - 1

3. **输出**:
   - pixel_values: torch.Tensor，形状为[1, img_key数量 * img_steps, C, config.image_size, config.image_size]
   - bboxs: Optional[np.ndarray]，形状为[img_key数量 * img_steps, 4]

#### 2.2.2 文本预处理

1. **输入**: raw_data.instruction (str)
2. **处理步骤**: 使用tokenizer对指令进行分词处理，添加特殊tokens
3. **输出**: text_ids (List[int])

#### 2.2.3 机器人状态预处理

1. **输入**: proprio: np.ndarray，形状为[proprio_len, proprio_dim]

2. **处理步骤**:
   - 使用`robot_tokenizer.proprio`对历史本体感知数据进行编码
   - 使用`robot_tokenizer.proprio`对当前本体感知数据进行编码
   - 使用`robot_tokenizer.norm_proprio`对本体感知数据进行归一化

3. **输出**:
   - hist_proprio: np.ndarray，形状为[proprio_len-1, proprio_dim]
   - cur_proprio: np.ndarray，形状为[proprio_dim]
   - normed_proprio: torch.Tensor，形状为[1, proprio_len, proprio_dim]

#### 2.2.4 Token模式处理

1. **输入**: 多种数据类型（text_ids, proprio等）

2. **处理步骤**:
   - 使用`TokenPattern.update_tokens`方法根据预定义模式生成input_ids和robot_input_ids
   - 根据配置确定哪些tokens参与损失计算（est=True/False）
   - 根据配置确定哪些tokens作为输入（as_input=True/False）

3. **输出**:
   - input_ids: torch.Tensor，形状为[1, N_token]
   - robot_input_ids: torch.Tensor，形状为[1, N_robot_token]
   - attention_mask: torch.Tensor，形状为[1, N_token]
   - robot_attention_mask: torch.Tensor，形状为[1, N_robot_token]

#### 2.2.5 构建BatchVLAData

将所有预处理后的数据打包成BatchVLAData对象：

- debug: List[Any]
- input_ids: torch.Tensor，形状为[1, N_token]
- robot_input_ids: torch.Tensor，形状为[1, N_robot_token]
- labels: None（在预处理阶段未设置）
- robot_labels: None（在预处理阶段未设置）
- attention_mask: torch.Tensor，形状为[1, N_token]
- robot_attention_mask: torch.Tensor，形状为[1, N_robot_token]
- images: torch.Tensor，形状为[1, img_key数量 * img_steps, C, config.image_size, config.image_size]
- action: None（在预处理阶段未设置）
- proprio: torch.Tensor，形状为[1, proprio_len, proprio_dim]
- goal: None（在预处理阶段未设置）
- is_action: torch.Tensor，形状为[1]
- inference_kwargs: List[dict]

### 2.3 数据维度变化总结

| 阶段 | 数据类型 | 原始维度 | 预处理后维度 | 主要变化 |
|------|----------|----------|--------------|----------|
| 图像预处理 | images | [img_key数量, img_steps, H, W, C] | [1, img_key数量 * img_steps, C, config.image_size, config.image_size] | 统一尺寸、增加batch维度 |
| 图像预处理 | bboxs | [img_key数量, img_steps, 4] | [img_key数量 * img_steps, 4] | 扁平化处理 |
| 文本预处理 | instruction | str | List[int] | 分词处理 |
| 状态预处理 | proprio | [proprio_len, proprio_dim] | [1, proprio_len, proprio_dim] | 增加batch维度 |
| Token处理 | 多种 | 多种 | input_ids: [1, N_token] | 统一序列格式、增加batch维度 |

### 2.4 数据批处理 (vla_data_collator.py)

在训练时，使用`vla_collator`函数将多个BatchVLAData对象合并成一个批次：

1. **处理步骤**:
   - 对input_ids和robot_input_ids进行padding，确保序列长度一致
   - 对attention_mask和robot_attention_mask进行相应处理
   - 对images、action、proprio、goal等张量进行拼接

2. **输出维度**:
   - input_ids: [batch_size, max_seq_len]
   - robot_input_ids: [batch_size, max_robot_seq_len]
   - attention_mask: [batch_size, max_seq_len]
   - robot_attention_mask: [batch_size, max_robot_seq_len]
   - images: [batch_size, img_key数量 * img_steps, C, config.image_size, config.image_size]
   - proprio: [batch_size, proprio_len, proprio_dim]

---

## 3. 模型架构详解

### 3.1 视觉特征提取 (DinoSigLIPViTBackbone)

#### 3.1.1 双编码器架构

GraspVLA算法使用了DinoSigLIPViTBackbone作为视觉特征提取器，该网络结合了两个预训练的Vision Transformer模型：
- **DINOv2** (vit_large_patch14_reg4_dinov2.lvd142m): 提供强大的自监督视觉表示
- **SigLIP** (vit_so400m_patch14_siglip_224/384): 提供视觉-语言对齐的表示

#### 3.1.2 特征提取流程

```mermaid
graph TD
    A[输入图像<br/>B×N_views×3×H×W] --> B[预处理阶段]
    B --> C[DINO预处理]
    B --> D[SigLIP预处理]
    
    C --> E[DINOv2 ViT<br/>倒数第二层特征]
    D --> F[SigLIP ViT<br/>倒数第二层特征]
    
    E --> G[DINO特征<br/>B×num_patches×1024]
    F --> H[SigLIP特征<br/>B×num_patches×1024]
    
    G --> I[特征拼接<br/>B×num_patches×2048]
    H --> I
    
    style A fill:#e1f5fe
    style I fill:#c8e6c9
```

**详细步骤：**

1. **预处理阶段**：
   - 输入图像首先通过`CombineImageTransform`进行预处理
   - 包含两个独立的预处理流水线（DINO和SigLIP各一个）
   - 每个预处理流水线使用对应模型的特定变换

2. **特征提取阶段**：
   - 预处理后的图像分别输入到DINO和SigLIP模型中
   - 每个模型通过`ViT`封装类提取倒数第二层的中间特征
   - 提取的特征保留了空间信息（patch级别的特征）

3. **特征融合阶段**：
   - 将DINO和SigLIP提取的特征在最后一个维度上进行拼接
   - 得到融合后的视觉特征：(batch_size, num_patches, 2048)

### 3.2 语言模型处理 (InternLM2)

#### 3.2.1 模型结构

InternLM2-1_8b语言模型的核心组件：

- **InternLM2Attention**: 多头注意力机制，支持RoPE位置编码
- **InternLM2MLP**: 前馈网络，使用SwiGLU激活函数
- **InternLM2RMSNorm**: RMS归一化层
- **InternLM2RotaryEmbedding**: 旋转位置编码(RoPE)

#### 3.2.2 配置参数

- 隐藏层大小: 4096
- 注意力头数: 32
- 层数: 32
- 词表大小: 103168
- 最大序列长度: 2048

#### 3.2.3 处理流程

```mermaid
graph TD
    A[文本指令] --> B[InternLM2TokenizerFast<br/>分词处理]
    B --> C[Token IDs<br/>sequence_length]
    C --> D[词嵌入层<br/>tok_embeddings]
    D --> E[词向量<br/>B×seq_len×4096]
    
    F[视觉特征] --> G[FusedMLPProjector<br/>特征投影]
    G --> H[投影特征<br/>B×img_tokens×4096]
    
    E --> I[多模态融合<br/>特征拼接]
    H --> I
    I --> J[InternLM2解码器<br/>32层Transformer]
    J --> K[输出特征<br/>B×total_seq_len×4096]
    
    style A fill:#e1f5fe
    style K fill:#c8e6c9
    style I fill:#fff3e0
```

### 3.3 特征投影器 (FusedMLPProjector)

#### 3.3.1 投影器结构

将视觉特征从2048维投影到LLM的4096维空间：

```python
# 投影器结构
Linear(2048 → 8192)  # 4倍扩展
→ GELU激活函数
→ Linear(8192 → 4096) # 映射到LLM维度
→ GELU激活函数  
→ Linear(4096 → 4096) # 最终投影
```

#### 3.3.2 维度变化

- 输入：视觉特征 [B, num_patches, 2048]
- 中间层1：[B, num_patches, 8192]
- 中间层2：[B, num_patches, 4096]
- 输出：投影特征 [B, num_patches, 4096]

### 3.4 VLA流匹配模块 (VLAFlowMatchingModule)

#### 3.4.1 流匹配原理

流匹配是一种生成模型技术，通过学习从噪声分布到数据分布的连续变换路径：

```mermaid
graph LR
    A[噪声分布<br/>t=1] --> B[中间状态<br/>t=0.5] --> C[数据分布<br/>t=0]
    A -.->|流场向量| C
    
    style A fill:#ffcdd2
    style C fill:#c8e6c9
    style B fill:#fff3e0
```

#### 3.4.2 核心算法

**扩散过程：**
```python
def diffuse(self, x_1, t, noise=None):
    if noise is None:
        noise = torch.randn_like(x_1)
    x_t = t * noise + (1 - t) * x_1  # 线性插值
    u_t = noise - x_1                # 速度目标
    return x_t, u_t
```

**去噪过程：**
```python
def denoise(self, compute_v_t, x_t, iter_num):
    dt = 1.0 / iter_num
    time_steps = torch.linspace(1.0, dt, iter_num)
    for t in time_steps:
        v_t = compute_v_t(x_t, t)
        x_t = x_t + (-dt) * v_t  # 欧拉积分更新
    return x_t
```

### 3.5 关键技术实现细节

#### 3.5.1 Token Pattern设计

```python
# CoT Action Pattern
TokenPattern(
    infos=[
        TokenInfo(key='text_ids', est=False, as_input=True),      # 输入文本
        TokenInfo(key='bbox', length=8, est=True, as_input=False), # 生成边界框
        TokenInfo(key='hist_proprio', est=False, as_input=True),   # 历史本体感知
        TokenInfo(key='cur_proprio', est=False, as_input=True),    # 当前本体感知
        TokenInfo(key='goal', length=6, est=True, as_input=False), # 生成目标
        TokenInfo(key='eos', length=1, est=True, as_input=False),  # 结束符
    ]
)
```

#### 3.5.2 注意力掩码机制

```python
def make_block_attn_mask(input_mask, block_mask):
    # 创建因果掩码
    causal_mask = torch.tril(torch.ones((seq_len, seq_len)))
    
    # 添加块级掩码
    if block_mask.any():
        block_attn_mask = create_block_attention(block_mask)
        causal_mask = combine_masks(causal_mask, block_attn_mask)
    
    return causal_mask
```

#### 3.5.3 关键组件说明

**RobotTokenizer**：
负责对机器人相关的连续值（如proprio、action）进行离散化处理：
- `UniformRobotTokenizer`: 使用均匀分布进行分箱
- `RatioMinMaxUniformRobotTokenizer`: 使用百分位数确定范围后进行均匀分箱

---

## 4. 推理流程

### 4.1 端到端推理pipeline

```mermaid
graph TD
    A[原始输入数据] --> B[RawVLAData构建]
    B --> C[数据预处理器]
    C --> D[图像变换]
    C --> E[文本Tokenization]
    C --> F[本体感知归一化]
    
    D --> G[BatchVLAData]
    E --> G
    F --> G
    
    G --> H[VLA数据整理器]
    H --> I[模型输入张量]
    
    I --> J[视觉编码器]
    I --> K[语言模型嵌入]
    I --> L[本体感知处理]
    
    J --> M[特征投影器]
    M --> N[多模态嵌入融合]
    K --> N
    L --> N
    
    N --> O[自回归生成]
    O --> P[边界框预测]
    O --> Q[目标位置预测]
    
    Q --> R[流匹配初始化]
    L --> R
    R --> S[迭代去噪过程]
    S --> T[最终动作输出]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style T fill:#9f9,stroke:#333,stroke-width:2px
```

### 4.2 推理阶段详细步骤

#### 4.2.1 输入准备阶段

1. **原始数据构建** (`RawVLAData`)
   ```python
   raw_data = RawVLAData(
       instruction="抓取红色杯子",
       images={"front": np.array(...), "side": np.array(...)},
       proprio=np.array([x, y, z, roll, pitch, yaw, gripper])
   )
   ```

2. **数据预处理** (`DataPreprocessor.transform`)
   - 图像预处理：调整大小到224×224，归一化
   - 文本tokenization：转换为token ID序列
   - 本体感知归一化：映射到[-1, 1]区间

3. **批处理整理** (`vla_collator`)
   - 填充序列到统一长度
   - 创建注意力掩码
   - 组织成PyTorch张量

#### 4.2.2 模型前向推理阶段

1. **视觉特征提取**
   ```python
   # 在 VLA.get_proj_feat_2d()
   feat_2d = self.backbone_2d(images)  # DinoSigLIP ViT
   proj_feat_2d = self.projector(feat_2d)  # 投影到LLM空间
   ```

2. **多模态嵌入融合**
   ```python
   # 在 VLA.embed_prefix()
   input_embed = self.llm.input_embedding(input_ids)
   mm_input_embed = self.insert_img_info(input_embed, proj_feat_2d)
   ```

3. **自回归生成感知Token**
   ```python
   # 在 VLA.generate_autoregressive()
   cot_parse, kv_cache = self.generate_autoregressive(
       input_ids=input_ids,
       proj_feat_2d=proj_feat_2d,
       token_pattern=token_pattern,
       max_token_num=100
   )
   ```

4. **流匹配动作生成**
   ```python
   # 在 VLA.generate_flow_matching()
   action = self.generate_flow_matching(
       prefix_kv_cache=kv_cache,
       prefix_mask=prefix_mask,
       proprio=proprio,
       flow_matching_iter=10
   )
   ```

#### 4.2.3 输出后处理阶段

```mermaid
graph TD
    A[模型原始输出] --> B[Token结果解析]
    A --> C[动作结果处理]
    
    B --> D[边界框逆标记化]
    B --> E[目标位置逆标记化]
    C --> F[动作逆归一化]
    
    D --> G[最终结果字典]
    E --> G
    F --> G
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#9f9,stroke:#333,stroke-width:2px
```

**输出处理步骤：**

1. **动作逆变换**
   ```python
   # 在 VLAAgent.sample_action()
   ret['action'] = self.preprocessor.robot_tokenizer.inv_norm_action(
       action_result.float().cpu().numpy()[0]
   )
   ```

2. **感知结果解析**
   ```python
   if hasattr(token_result, 'goal'):
       goal = self.preprocessor.robot_tokenizer.inv_goal(np.array(token_result.goal))
       ret['goal'] = (goal[:3], goal[3:6])  # 位置和姿态
   
   if hasattr(token_result, 'bbox'):
       ret['bbox'] = (self.preprocessor.robot_tokenizer.uniform_tokenizer
                     .uniform_detokenize(np.array(token_result.bbox).reshape(-1, 4)) + 1)/2*224
   ```

### 4.3 实时推理流程

#### 4.3.1 实时数据预处理

**视觉输入处理** ([3,480,640]的实时图像):
1. 输入：实时RGB图像，尺寸为[3,480,640]
2. 调整大小：使用`resize_with_bbox`函数将图像调整为224x224
3. 填充：如果需要，对图像进行填充以保持宽高比
4. 归一化：使用`image_transform`将像素值归一化到[-1,1]范围
5. 维度变换：最终得到形状为[B, T_image, N_backbone, C, H, W]的张量

**语言指令处理** ("现在把箱子移到B点"):
1. 指令格式化：原始指令格式化为CoT提示："In: What action should the robot take to 现在把箱子移到B点?\nOut: "
2. 文本分词：使用InternLM2分词器将格式化后的指令转换为token IDs

#### 4.3.2 实时推理执行流程

1. **数据准备**：
   - 捕获当前视觉输入和本体感知信息
   - 格式化语言指令

2. **前向传播**：
   - 视觉特征提取和投影
   - 文本嵌入生成
   - 多模态特征融合
   - CoT自回归生成
   - Flow Matching动作生成

3. **后处理**：
   - 动作反归一化：将离散化的动作token转换为连续动作空间
   - 动作插值：将生成的动作序列插值为更细粒度的控制指令
   - 执行动作：将最终动作序列发送给机器人执行

---

## 5. 训练流程

### 5.1 数据准备阶段

```mermaid
graph TD
    A[训练数据集] --> B[数据加载器]
    B --> C[批量采样]
    C --> D[数据预处理]
    D --> E[动作标记化]
    D --> F[图像增强]
    D --> G[文本处理]
    
    E --> H[训练批次]
    F --> H
    G --> H
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#9f9,stroke:#333,stroke-width:2px
```

### 5.2 前向传播阶段

```mermaid
graph TD
    A[训练批次] --> B[多模态嵌入]
    B --> C[自回归损失计算]
    B --> D[流匹配损失计算]
    
    C --> E[感知Token损失]
    D --> F[动作重建损失]
    
    E --> G[总损失]
    F --> G
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#9f9,stroke:#333,stroke-width:2px
```

### 5.3 损失计算详解

#### 5.3.1 自回归损失 (感知Token)

```python
# 边界框和目标位置的交叉熵损失
autoregressive_loss = CrossEntropyLoss(
    predictions=token_predictions,
    targets=token_labels,
    ignore_index=IGNORE_INDEX
)
```

#### 5.3.2 流匹配损失 (动作生成)

```python
# 速度预测的MSE损失
def flow_matching_loss(self, action, proprio):
    # 采样时间和噪声
    x_t, u_t, time = self.flow_module.sample_noise_and_time(action)
    
    # 预测速度
    v_t_pred = self.compute_v_t(x_t, time, proprio)
    
    # 计算损失
    loss = F.mse_loss(v_t_pred, u_t)
    return loss
```

### 5.4 反向传播和优化

```mermaid
graph TD
    A[总损失] --> B[梯度计算]
    B --> C[梯度裁剪]
    C --> D[参数更新]
    D --> E[学习率调度]
    
    E --> F[下一个训练步骤]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#9f9,stroke:#333,stroke-width:2px
```

---

## 6. 数据采集

### 6.1 训练数据收集详细说明

#### 6.1.1 视觉输入数据采集

**RGB-D摄像头设置**：
- 设备配置：部署多个RGB-D摄像头（如Intel RealSense D435）围绕机器人工作区域
- 视角布局：
  - 前视图摄像头：位于机器人正前方，捕捉主要操作区域
  - 侧视图摄像头：位于机器人侧面，提供侧面视角
  - 俯视图摄像头：位于机器人上方，提供俯视视角（可选）
- 参数设置：
  - 分辨率：1280×720（RGB）或640×480（深度）
  - 帧率：30fps
  - 深度范围：0.2m-10m
  - 精度：±2% at 2m distance

**图像采集流程**：
1. 同步采集：同时从多个摄像头采集RGB图像和深度图像
2. 图像预处理：将RGB图像和深度图像对齐，应用相机标定参数校正图像畸变
3. 箱子检测标注：手动或自动标注箱子在图像中的边界框位置

#### 6.1.2 语言指令数据采集

**指令多样性设计**：
1. 直接指令："将红色箱子从A点移动到B点"
2. 描述性指令："将桌子上的红色方块移动到指定位置"
3. 条件指令："如果箱子是红色的，请把它从A点移动到B点"
4. 多步骤指令："先拿起红色箱子，然后把它放在B点"

#### 6.1.3 动作标签数据采集

**机器人状态采集**：
1. 本体感知数据：
   - 关节角度：7自由度机械臂各关节角度
   - 夹爪状态：开合程度（0-1范围）
   - 末端执行器位姿：xyz坐标和roll/pitch/yaw角度

2. 采集频率：每10ms采集一次机器人状态，与图像采集同步

**动作序列标注**：
1. 连续动作空间：7维动作向量：[x, y, z, roll, pitch, yaw, gripper]
2. 动作序列标注：记录从任务开始到完成的完整动作序列

### 6.2 数据格式和存储方式

#### 6.2.1 数据格式定义

根据GraspVLA的`RawVLAData`数据结构：

```python
{
    # 指令信息
    "instruction": "将红色箱子从A点移动到B点",
    "can_be_anything": False,
    
    # 观测信息
    "images": {
        "front": np.ndarray,  # 前视图RGB图像 [H, W, 3]
        "side": np.ndarray,   # 侧视图RGB图像 [H, W, 3]
        "depth_front": np.ndarray,  # 前视图深度图像 [H, W]
        "depth_side": np.ndarray    # 侧视图深度图像 [H, W]
    },
    "bboxs": {
        "front": np.ndarray,  # 前视图边界框 [x1, y1, x2, y2]
        "side": np.ndarray    # 侧视图边界框 [x1, y1, x2, y2]
    },
    "proprio": np.ndarray,  # 本体感知数据 [proprio_len, proprio_dim]
    
    # 动作信息
    "action": np.ndarray,   # 动作序列 [action_len, action_dim]
    "goal": np.ndarray      # 目标状态 [goal_dim]
}
```

#### 6.2.2 存储方式

1. **文件格式**：
   - 图像数据：JPEG格式存储
   - 数值数据：HDF5格式存储
   - 元数据：JSON格式存储指令和标注信息

2. **目录结构**：
```
dataset/
├── episodes/
│   ├── episode_000001/
│   │   ├── images/
│   │   │   ├── front_rgb.jpg
│   │   │   ├── side_rgb.jpg
│   │   │   ├── front_depth.png
│   │   │   └── side_depth.png
│   │   ├── data.h5      # 数值数据
│   │   └── metadata.json # 元数据
│   └── episode_000002/
└── dataset_info.json    # 数据集信息
```

### 6.3 语言指令在时间步中的共享和使用

#### 6.3.1 指令处理方式

1. **统一指令格式**：所有时间步共享同一个语言指令，通过`COT_PROMPT`函数包装成统一格式："In: What action should the robot take to {prompt}?\nOut: "

2. **CoT推理框架**：语言指令在推理过程中被用作上下文，模型会基于：
   - 语言指令
   - 历史本体感知数据
   - 当前观测数据
   来生成动作序列

3. **时间步间共享**：语言指令在所有时间步中保持不变，但模型会在每个时间步基于当前状态重新理解指令并生成相应动作

---

## 7. 性能优化和部署

### 7.1 推理优化

- **KV缓存**: 避免重复计算注意力
- **FlexAttention**: 高效的注意力实现
- **模型编译**: 使用torch.compile加速
- **混合精度**: bfloat16推理

### 7.2 训练优化

- **梯度检查点**: 减少显存占用
- **数据并行**: 多GPU训练
- **梯度累积**: 模拟更大批次
- **动态填充**: 减少计算浪费

### 7.3 使用示例

#### 7.3.1 推理使用

```python
# 创建VLA代理
vla_agent = VLAAgent(path="model.safetensors")

# 准备输入数据
raw_data = RawVLAData(
    instruction="抓取桌上的红色杯子",
    images={"front": front_image, "side": side_image},
    proprio=current_joint_states
)

# 执行推理
result = vla_agent.sample_action(raw_data)
action = result['action']  # 7维动作向量
goal = result['goal']      # 目标位置和姿态
bbox = result['bbox']      # 检测边界框
```

#### 7.3.2 服务部署

```python
# 启动推理服务
python vla_network/scripts/serve.py --path model.safetensors --compile
```

---

## 总结

GraspVLA通过多阶段、多模态的处理方式，实现了从自然语言指令到机器人精确动作的端到端映射。该架构的关键创新点包括：

1. **双编码器视觉系统**：融合DINOv2和SigLIP的优势，提供更丰富的视觉表示
2. **思维链推理框架**：通过CoT生成中间感知结果，增强模型推理能力
3. **流匹配动作生成**：使用连续流匹配技术生成平滑精确的动作轨迹
4. **模块化设计**：各组件职责清晰，便于维护和扩展

整个系统在仅使用合成数据预训练的情况下，实现了对真实世界任务的零样本泛化能力，无需微调即可直接应用于实际机器人控制。
