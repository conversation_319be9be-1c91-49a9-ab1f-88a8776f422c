# GraspVLA 端到端训练和推理流程详解

## 1. 推理流程 (Inference Pipeline)

### 1.1 输入准备阶段

```mermaid
graph TD
    A[原始输入数据] --> B[RawVLAData构建]
    B --> C[数据预处理器]
    C --> D[图像变换]
    C --> E[文本Tokenization]
    C --> F[本体感知归一化]
    
    D --> G[BatchVLAData]
    E --> G
    F --> G
    
    G --> H[VLA数据整理器]
    H --> I[模型输入张量]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style I fill:#9f9,stroke:#333,stroke-width:2px
```

**详细步骤：**

1. **原始数据构建** (`RawVLAData`)
   ```python
   raw_data = RawVLAData(
       instruction="抓取红色杯子",
       images={"front": np.array(...), "side": np.array(...)},
       proprio=np.array([x, y, z, roll, pitch, yaw, gripper])
   )
   ```

2. **数据预处理** (`DataPreprocessor.transform`)
   - 图像预处理：调整大小到224×224，归一化
   - 文本tokenization：转换为token ID序列
   - 本体感知归一化：映射到[-1, 1]区间

3. **批处理整理** (`vla_collator`)
   - 填充序列到统一长度
   - 创建注意力掩码
   - 组织成PyTorch张量

### 1.2 模型前向推理阶段

```mermaid
graph TD
    A[模型输入张量] --> B[视觉编码器]
    A --> C[语言模型嵌入]
    A --> D[本体感知处理]
    
    B --> E[特征投影器]
    E --> F[多模态嵌入融合]
    C --> F
    D --> F
    
    F --> G[自回归生成]
    G --> H[边界框预测]
    G --> I[目标位置预测]
    
    I --> J[流匹配初始化]
    D --> J
    J --> K[迭代去噪过程]
    K --> L[最终动作输出]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style L fill:#9f9,stroke:#333,stroke-width:2px
```

**核心推理步骤：**

1. **视觉特征提取**
   ```python
   # 在 VLA.get_proj_feat_2d()
   feat_2d = self.backbone_2d(images)  # DinoSigLIP ViT
   proj_feat_2d = self.projector(feat_2d)  # 投影到LLM空间
   ```

2. **多模态嵌入融合**
   ```python
   # 在 VLA.embed_prefix()
   input_embed = self.llm.input_embedding(input_ids)
   mm_input_embed = self.insert_img_info(input_embed, proj_feat_2d)
   ```

3. **自回归生成感知Token**
   ```python
   # 在 VLA.generate_autoregressive()
   cot_parse, kv_cache = self.generate_autoregressive(
       input_ids=input_ids,
       proj_feat_2d=proj_feat_2d,
       token_pattern=token_pattern,
       max_token_num=100
   )
   ```

4. **流匹配动作生成**
   ```python
   # 在 VLA.generate_flow_matching()
   action = self.generate_flow_matching(
       prefix_kv_cache=kv_cache,
       prefix_mask=prefix_mask,
       proprio=proprio,
       flow_matching_iter=10
   )
   ```

### 1.3 输出后处理阶段

```mermaid
graph TD
    A[模型原始输出] --> B[Token结果解析]
    A --> C[动作结果处理]
    
    B --> D[边界框逆标记化]
    B --> E[目标位置逆标记化]
    C --> F[动作逆归一化]
    
    D --> G[最终结果字典]
    E --> G
    F --> G
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#9f9,stroke:#333,stroke-width:2px
```

**输出处理步骤：**

1. **动作逆变换**
   ```python
   # 在 VLAAgent.sample_action()
   ret['action'] = self.preprocessor.robot_tokenizer.inv_norm_action(
       action_result.float().cpu().numpy()[0]
   )
   ```

2. **感知结果解析**
   ```python
   if hasattr(token_result, 'goal'):
       goal = self.preprocessor.robot_tokenizer.inv_goal(np.array(token_result.goal))
       ret['goal'] = (goal[:3], goal[3:6])  # 位置和姿态
   
   if hasattr(token_result, 'bbox'):
       ret['bbox'] = (self.preprocessor.robot_tokenizer.uniform_tokenizer
                     .uniform_detokenize(np.array(token_result.bbox).reshape(-1, 4)) + 1)/2*224
   ```

## 2. 训练流程 (Training Pipeline)

### 2.1 数据准备阶段

```mermaid
graph TD
    A[训练数据集] --> B[数据加载器]
    B --> C[批量采样]
    C --> D[数据预处理]
    D --> E[动作标记化]
    D --> F[图像增强]
    D --> G[文本处理]
    
    E --> H[训练批次]
    F --> H
    G --> H
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#9f9,stroke:#333,stroke-width:2px
```

### 2.2 前向传播阶段

```mermaid
graph TD
    A[训练批次] --> B[多模态嵌入]
    B --> C[自回归损失计算]
    B --> D[流匹配损失计算]
    
    C --> E[感知Token损失]
    D --> F[动作重建损失]
    
    E --> G[总损失]
    F --> G
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style G fill:#9f9,stroke:#333,stroke-width:2px
```

### 2.3 损失计算详解

**1. 自回归损失 (感知Token)**
```python
# 边界框和目标位置的交叉熵损失
autoregressive_loss = CrossEntropyLoss(
    predictions=token_predictions,
    targets=token_labels,
    ignore_index=IGNORE_INDEX
)
```

**2. 流匹配损失 (动作生成)**
```python
# 速度预测的MSE损失
def flow_matching_loss(self, action, proprio):
    # 采样时间和噪声
    x_t, u_t, time = self.flow_module.sample_noise_and_time(action)
    
    # 预测速度
    v_t_pred = self.compute_v_t(x_t, time, proprio)
    
    # 计算损失
    loss = F.mse_loss(v_t_pred, u_t)
    return loss
```

### 2.4 反向传播和优化

```mermaid
graph TD
    A[总损失] --> B[梯度计算]
    B --> C[梯度裁剪]
    C --> D[参数更新]
    D --> E[学习率调度]
    
    E --> F[下一个训练步骤]
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#9f9,stroke:#333,stroke-width:2px
```

## 3. 关键技术实现细节

### 3.1 Token Pattern设计

```python
# CoT Action Pattern
TokenPattern(
    infos=[
        TokenInfo(key='text_ids', est=False, as_input=True),      # 输入文本
        TokenInfo(key='bbox', length=8, est=True, as_input=False), # 生成边界框
        TokenInfo(key='hist_proprio', est=False, as_input=True),   # 历史本体感知
        TokenInfo(key='cur_proprio', est=False, as_input=True),    # 当前本体感知
        TokenInfo(key='goal', length=6, est=True, as_input=False), # 生成目标
        TokenInfo(key='eos', length=1, est=True, as_input=False),  # 结束符
    ]
)
```

### 3.2 流匹配算法核心

**扩散过程：**
```python
def diffuse(self, x_1, t, noise=None):
    if noise is None:
        noise = torch.randn_like(x_1)
    x_t = t * noise + (1 - t) * x_1  # 线性插值
    u_t = noise - x_1                # 速度目标
    return x_t, u_t
```

**去噪过程：**
```python
def denoise(self, compute_v_t, x_t, iter_num):
    dt = 1.0 / iter_num
    time_steps = torch.linspace(1.0, dt, iter_num)
    for t in time_steps:
        v_t = compute_v_t(x_t, t)
        x_t = x_t + (-dt) * v_t  # 欧拉积分更新
    return x_t
```

### 3.3 注意力掩码机制

```python
def make_block_attn_mask(input_mask, block_mask):
    # 创建因果掩码
    causal_mask = torch.tril(torch.ones((seq_len, seq_len)))
    
    # 添加块级掩码
    if block_mask.any():
        block_attn_mask = create_block_attention(block_mask)
        causal_mask = combine_masks(causal_mask, block_attn_mask)
    
    return causal_mask
```

## 4. 性能优化策略

### 4.1 推理优化
- **KV缓存**: 避免重复计算注意力
- **FlexAttention**: 高效的注意力实现
- **模型编译**: 使用torch.compile加速
- **混合精度**: bfloat16推理

### 4.2 训练优化
- **梯度检查点**: 减少显存占用
- **数据并行**: 多GPU训练
- **梯度累积**: 模拟更大批次
- **动态填充**: 减少计算浪费

## 5. 使用示例

### 5.1 推理使用
```python
# 创建VLA代理
vla_agent = VLAAgent(path="model.safetensors")

# 准备输入数据
raw_data = RawVLAData(
    instruction="抓取桌上的红色杯子",
    images={"front": front_image, "side": side_image},
    proprio=current_joint_states
)

# 执行推理
result = vla_agent.sample_action(raw_data)
action = result['action']  # 7维动作向量
goal = result['goal']      # 目标位置和姿态
bbox = result['bbox']      # 检测边界框
```

### 5.2 服务部署
```python
# 启动推理服务
python vla_network/scripts/serve.py --path model.safetensors --compile
```

这个端到端流程展示了GraspVLA从原始输入到最终动作输出的完整处理链路，包括训练时的损失计算和优化策略。